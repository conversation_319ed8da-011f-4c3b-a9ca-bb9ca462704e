{"current_level": 5, "score": 8700, "high_score": 8700, "player_data": {"health": 89, "max_health": 100, "damage": 45, "speed": 6, "fire_rate": 450, "level": 7, "xp": 45, "xp_to_next_level": 1135, "upgrade_points": 5, "progression_data": {"skill_tree": {"skill_points": 1, "learned_skills": {"critical_strike": 1, "multi_shot": 3, "piercing_shots": 1}, "active_synergies": []}, "equipment_manager": {"equipped": {"weapon": null, "armor": {"equipment_type": "armor", "name": "Fortress", "rarity": "Common", "level": 1, "stats": {"damage_reduction": 0.47}}, "accessory": {"equipment_type": "accessory", "name": "Relic", "rarity": "Rare", "level": 1, "stats": {"xp_bonus": 1.77, "item_find": 1.7, "resource_bonus": 2.85}}}, "inventory": [{"equipment_type": "accessory", "name": "Talisman", "rarity": "Rare", "level": 1, "stats": {"resource_bonus": 2.36}}, {"equipment_type": "accessory", "name": "Totem", "rarity": "Common", "level": 1, "stats": {"skill_cooldown": 0.52, "resource_bonus": 1.08}}, {"equipment_type": "accessory", "name": "Relic", "rarity": "Rare", "level": 1, "stats": {"item_find": 1.35, "xp_bonus": 1.48, "resource_bonus": 2.29}}, {"equipment_type": "accessory", "name": "Artifact", "rarity": "Uncommon", "level": 1, "stats": {"xp_bonus": 1.21, "resource_bonus": 1.9, "item_find": 1.15}}, {"equipment_type": "armor", "name": "Fortress", "rarity": "Common", "level": 1, "stats": {"speed_bonus": 4, "health_bonus": 154, "damage_reduction": 0.76}}, {"equipment_type": "accessory", "name": "Amulet", "rarity": "Common", "level": 1, "stats": {"xp_bonus": 1.28}}, {"equipment_type": "accessory", "name": "Talisman", "rarity": "Uncommon", "level": 1, "stats": {"xp_bonus": 1.92, "item_find": 1.68, "resource_bonus": 2.38}}, {"equipment_type": "armor", "name": "Bulwark", "rarity": "Common", "level": 1, "stats": {"speed_bonus": 5, "regeneration": 9.27}}, {"equipment_type": "armor", "name": "Vest", "rarity": "Common", "level": 1, "stats": {"health_bonus": 186, "speed_bonus": 5}}]}, "achievement_manager": {"achievements": {"first_steps": {"name": "First Steps", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "experienced": {"name": "Experienced", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "veteran": {"name": "Veteran", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "master": {"name": "Master", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "first_blood": {"name": "First Blood", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "slayer": {"name": "Slayer", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "destroyer": {"name": "Destroyer", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "boss_hunter": {"name": "<PERSON>", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "boss_slayer": {"name": "Boss Slayer", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "survivor": {"name": "Survivor", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "untouchable": {"name": "Untouchable", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "iron_will": {"name": "Iron Will", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "skill_student": {"name": "Skill Student", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "skill_master": {"name": "Skill Master", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "well_equipped": {"name": "Well Equipped", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "fully_equipped": {"name": "Fully Equipped", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "collector": {"name": "Collector", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "hoarder": {"name": "Hoarder", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "speed_runner": {"name": "Speed Runner", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "perfectionist": {"name": "Perfectionist", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "secret_finder": {"name": "Secret Finder", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "lucky_shot": {"name": "Lucky Shot", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "enemy_slayer": {"name": "Enemy Slayer", "unlocked": false, "progress": 47, "achievement_type": "progressive", "max_progress": 100}, "damage_dealer": {"name": "Damage Dealer", "unlocked": false, "progress": 0, "achievement_type": "progressive", "max_progress": 10000}, "treasure_hunter": {"name": "Treasure Hunter", "unlocked": false, "progress": 11, "achievement_type": "progressive", "max_progress": 50}, "combat_master": {"name": "Combat Master", "unlocked": false, "progress": 0, "achievement_type": "chain", "max_progress": 1}, "immortal": {"name": "<PERSON><PERSON><PERSON><PERSON>", "unlocked": false, "progress": 0, "achievement_type": "chain", "max_progress": 1}, "master_explorer": {"name": "Master Explorer", "unlocked": false, "progress": 0, "achievement_type": "chain", "max_progress": 1}}, "completed_chains": []}, "stats": {"enemies_killed": 47, "bosses_killed": 0, "levels_completed": 0, "perfect_levels": 0, "near_death_survivals": 0, "skills_learned": 0, "maxed_skills": 0, "maxed_combat_skills": 0, "equipment_equipped": 11, "full_equipment_sets": 0, "items_collected": 11, "secrets_found": 0, "max_crit_streak": 2, "current_crit_streak": 0, "fastest_level_time": Infinity, "player_level": 7, "total_damage_dealt": 0}, "regen_timer": 0}}}